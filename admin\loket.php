<?php
// Admin counter management page (Mock Version)
$page_title = 'Manajemen Loket';
$active_menu = 'loket';
$is_admin = true;
require_once '../includes/functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('../login_admin.php');
}

// Check if user is admin
if (!is_admin()) {
    redirect('../index.php');
}

// Process form submission (mock)
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['add_counter'])) {
        $_SESSION['success_message'] = 'Loket berhasil ditambahkan.';
        redirect('loket.php');
    } elseif (isset($_POST['edit_counter'])) {
        $_SESSION['success_message'] = 'Loket berhasil diperbarui.';
        redirect('loket.php');
    } elseif (isset($_POST['delete_counter'])) {
        $_SESSION['success_message'] = 'Loket berhasil dihapus.';
        redirect('loket.php');
    }
}

// Mock data for counters
$counters = [
    [
        'id' => 1,
        'nama_loket' => 'Loket 1',
        'jenis_layanan' => 'teller',
        'status' => 'aktif',
        'user_id' => 2,
        'nama_petugas' => 'Petugas Teller 1'
    ],
    [
        'id' => 2,
        'nama_loket' => 'Loket 2',
        'jenis_layanan' => 'teller',
        'status' => 'aktif',
        'user_id' => null,
        'nama_petugas' => null
    ],
    [
        'id' => 3,
        'nama_loket' => 'Loket 3',
        'jenis_layanan' => 'teller',
        'status' => 'aktif',
        'user_id' => null,
        'nama_petugas' => null
    ],
    [
        'id' => 4,
        'nama_loket' => 'Loket 4',
        'jenis_layanan' => 'cs',
        'status' => 'aktif',
        'user_id' => 3,
        'nama_petugas' => 'Petugas CS 1'
    ],
    [
        'id' => 5,
        'nama_loket' => 'Loket 5',
        'jenis_layanan' => 'cs',
        'status' => 'aktif',
        'user_id' => 6,
        'nama_petugas' => 'Petugas CS 2'
    ],
    [
        'id' => 6,
        'nama_loket' => 'Loket 6',
        'jenis_layanan' => 'kredit',
        'status' => 'aktif',
        'user_id' => 4,
        'nama_petugas' => 'Petugas Kredit 1'
    ],
    [
        'id' => 7,
        'nama_loket' => 'Loket 7',
        'jenis_layanan' => 'kredit',
        'status' => 'nonaktif',
        'user_id' => null,
        'nama_petugas' => null
    ]
];

// Mock data for staff
$staff = [
    [
        'id' => 2,
        'username' => 'petugas1',
        'nama_lengkap' => 'Petugas Teller 1'
    ],
    [
        'id' => 3,
        'username' => 'petugas2',
        'nama_lengkap' => 'Petugas CS 1'
    ],
    [
        'id' => 4,
        'username' => 'petugas3',
        'nama_lengkap' => 'Petugas Kredit 1'
    ],
    [
        'id' => 5,
        'username' => 'petugas4',
        'nama_lengkap' => 'Petugas Teller 2'
    ],
    [
        'id' => 6,
        'username' => 'petugas5',
        'nama_lengkap' => 'Petugas CS 2'
    ]
];
?>

<?php
// Include header
include_once '../includes/header_mock.php';
?>

<style>
    .counter-table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .counter-actions .btn {
        margin-right: 5px;
        margin-bottom: 5px;
    }

    .badge-aktif {
        background-color: #28a745;
        color: white;
    }

    .badge-nonaktif {
        background-color: #dc3545;
        color: white;
    }

    .badge-teller {
        background-color: #007bff;
        color: white;
    }

    .badge-cs {
        background-color: #6f42c1;
        color: white;
    }

    .badge-kredit {
        background-color: #fd7e14;
        color: white;
    }

    .modal-header {
        background-color: #1a6a83;
        color: white;
    }

    .modal-header .close {
        color: white;
    }

    .form-group label {
        font-weight: 600;
    }

    .required::after {
        content: " *";
        color: red;
    }
</style>

            <!-- Content -->
            <div class="container-fluid">
                <div class="row mb-4">
                    <div class="col-md-8">
                        <h2>Manajemen Loket</h2>
                    </div>
                    <div class="col-md-4 text-right">
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addCounterModal">
                            <i class="fas fa-plus-circle mr-1"></i> Tambah Loket
                        </button>
                    </div>
                </div>

                <?php if (isset($_SESSION['success_message'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle mr-1"></i> <?php echo $_SESSION['success_message']; ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <?php unset($_SESSION['success_message']); ?>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle mr-1"></i> <?php echo $_SESSION['error_message']; ?>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <?php unset($_SESSION['error_message']); ?>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover counter-table">
                                <thead>
                                    <tr>
                                        <th width="5%">No</th>
                                        <th width="15%">Nama Loket</th>
                                        <th width="15%">Jenis Layanan</th>
                                        <th width="15%">Petugas</th>
                                        <th width="10%">Status</th>
                                        <th width="15%">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($counters as $index => $counter): ?>
                                    <tr>
                                        <td><?php echo $index + 1; ?></td>
                                        <td><?php echo $counter['nama_loket']; ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo $counter['jenis_layanan']; ?>">
                                                <?php
                                                    switch($counter['jenis_layanan']) {
                                                        case 'teller':
                                                            echo 'Teller';
                                                            break;
                                                        case 'cs':
                                                            echo 'Customer Service';
                                                            break;
                                                        case 'kredit':
                                                            echo 'Kredit';
                                                            break;
                                                        default:
                                                            echo '-';
                                                    }
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($counter['user_id']): ?>
                                            <?php echo $counter['nama_petugas']; ?>
                                            <?php else: ?>
                                            <span class="text-muted">Belum ditugaskan</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $counter['status']; ?>">
                                                <?php echo ucfirst($counter['status']); ?>
                                            </span>
                                        </td>
                                        <td class="counter-actions">
                                            <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#editCounterModal<?php echo $counter['id']; ?>">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#assignStaffModal<?php echo $counter['id']; ?>">
                                                <i class="fas fa-user-plus"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" data-toggle="modal" data-target="#deleteCounterModal<?php echo $counter['id']; ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Counter Modal -->
    <div class="modal fade" id="addCounterModal" tabindex="-1" role="dialog" aria-labelledby="addCounterModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCounterModalLabel">Tambah Loket</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="" method="post">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="nama_loket" class="required">Nama Loket</label>
                            <input type="text" class="form-control" id="nama_loket" name="nama_loket" required>
                        </div>
                        <div class="form-group">
                            <label for="jenis_layanan" class="required">Jenis Layanan</label>
                            <select class="form-control" id="jenis_layanan" name="jenis_layanan" required>
                                <option value="">-- Pilih Jenis Layanan --</option>
                                <option value="teller">Teller</option>
                                <option value="cs">Customer Service</option>
                                <option value="kredit">Kredit</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="status" class="required">Status</label>
                            <select class="form-control" id="status" name="status" required>
                                <option value="aktif">Aktif</option>
                                <option value="nonaktif">Non-aktif</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                        <button type="submit" name="add_counter" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

<?php
// Include footer
include_once '../includes/footer_mock.php';
?>
