<?php
// Booking page
$page_title = 'Booking Antrian';
require_once '../includes/functions.php'; // gunakan functions.php asli untuk booking real database

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in as nasabah
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'nasabah') {
    $_SESSION['error_message'] = 'Anda harus login terlebih dahulu untuk melakukan booking antrian.';
    redirect('login.php');
}

// Initialize messages
$success = false;
$error = '';
$error_messages = [];

// Process booking form
// HAPUS seluruh proses booking di file ini, karena form sudah diarahkan ke booking_process.php

// Get user data from session atau database
try {
    global $conn;

    // Selalu gunakan data dari session untuk nama
    // Ambil data nasabah berdasarkan user_id dari session
    $user_id = $_SESSION['user_id'];
    $sql = "SELECT * FROM nasabah WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        // Override nama dengan data dari session jika ada
        if (isset($_SESSION['user_name'])) {
            $user['nama'] = $_SESSION['user_name'];
        }
        if (isset($_SESSION['user_email'])) {
            $user['email'] = $_SESSION['user_email'];
        }
    } else {
        // Fallback to session data if database query fails
        $user = [
            'id' => $user_id,
            'nama' => $_SESSION['user_name'] ?? 'Nasabah',
            'email' => $_SESSION['user_email'] ?? '<EMAIL>',
            'no_identitas' => '****************',
            'no_hp' => '************'
        ];

        // Log the error
        error_log("Failed to fetch user data for ID: $user_id");
    }
} catch (Exception $e) {
    // Log the error
    error_log("Error fetching user data: " . $e->getMessage());

    // Fallback to session data
    $user = [
        'id' => $_SESSION['user_id'],
        'nama' => $_SESSION['user_name'] ?? 'Nasabah',
        'email' => $_SESSION['user_email'] ?? '<EMAIL>',
        'no_identitas' => '****************',
        'no_hp' => '************'
    ];
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Antrian - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .header {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 100;
        }

        .logo-container {
            display: flex;
            align-items: center;
        }

        .logo-container img {
            height: 50px;
            margin-right: 15px;
        }

        .logo-container h1 {
            color: white;
            font-size: 24px;
            margin: 0;
            font-weight: 600;
        }

        .nav-menu {
            display: flex;
            justify-content: flex-end;
        }

        .nav-menu a {
            color: white;
            margin-left: 20px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            padding: 8px 15px;
            border-radius: 50px;
        }

        .nav-menu a:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }

        .nav-menu a.active {
            background-color: white;
            color: #4caf50;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .nav-menu a i {
            margin-right: 5px;
        }

        .page-title {
            text-align: center;
            color: white;
            padding: 40px 0;
        }

        .page-title h2 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .page-title p {
            font-size: 18px;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto;
        }

        .booking-form-container {
            background-color: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 50px;
        }

        .form-title {
            text-align: center;
            margin-bottom: 30px;
        }

        .form-title h3 {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .form-title p {
            color: #666;
        }

        .form-group label {
            font-weight: 600;
            color: #555;
        }

        .form-control {
            border-radius: 8px;
            padding: 12px 15px;
            border: 1px solid #ddd;
        }

        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
            border-color: #4caf50;
        }

        .btn-submit {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            display: block;
            width: 100%;
            margin-top: 20px;
        }

        .btn-submit:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .service-option {
            display: none;
        }

        .service-option + label {
            display: block;
            padding: 15px;
            background-color: #f9f9f9;
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 15px;
        }

        .service-option + label:hover {
            border-color: #4caf50;
            background-color: #f0f9f0;
        }

        .service-option:checked + label {
            border-color: #4caf50;
            background-color: #e8f5e9;
            box-shadow: 0 4px 10px rgba(76, 175, 80, 0.2);
        }

        .service-option + label .service-icon {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            border-radius: 50%;
            color: white;
            text-align: center;
            line-height: 40px;
            margin-right: 15px;
            font-size: 18px;
        }

        .service-option + label .service-title {
            font-weight: 600;
            font-size: 18px;
            color: #333;
        }

        .service-option + label .service-desc {
            margin-top: 5px;
            color: #666;
            padding-left: 55px;
        }

        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .upload-area:hover {
            border-color: #4caf50;
            background-color: #f0f9f0;
        }

        .upload-icon {
            font-size: 48px;
            color: #4caf50;
            margin-bottom: 15px;
        }

        .upload-text h4 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .upload-text p {
            color: #666;
            font-size: 14px;
        }

        footer {
            background-color: #333;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        .footer-logo {
            height: 60px;
            margin-bottom: 20px;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .footer-links a {
            color: white;
            margin: 0 15px;
            text-decoration: none;
            transition: all 0.3s;
        }

        .footer-links a:hover {
            color: #4caf50;
        }

        .copyright {
            opacity: 0.7;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="logo-container">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB">
                        <h1>Bank BJB</h1>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="nav-menu">
                        <a href="index.php"><i class="fas fa-home"></i> Beranda</a>
                        <a href="booking.php" class="active"><i class="fas fa-calendar-check"></i> Booking</a>
                        <a href="check_status.php"><i class="fas fa-search"></i> Cek Status</a>
                        <a href="register.php"><i class="fas fa-user-plus"></i> Registrasi</a>
                        <a href="login.php"><i class="fas fa-sign-in-alt"></i> Login</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Page Title -->
    <div class="page-title">
        <div class="container">
            <h2>Booking Antrian</h2>
            <p>Isi formulir di bawah ini untuk melakukan booking antrian di Bank BJB</p>
        </div>
    </div>

    <!-- Booking Form -->
    <div class="container">
        <div class="booking-form-container">
            <div class="form-title">
                <h3>Formulir Booking Antrian</h3>
                <p>Silakan lengkapi data berikut dengan benar</p>
            </div>

            <?php if (!empty($error)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle mr-2"></i> <?php echo $error; ?>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <?php endif; ?>

            <?php if ($success): ?>
            <div class="alert alert-success">
                <h4><i class="fas fa-check-circle"></i> Booking Berhasil!</h4>
                <p>Nomor antrian Anda: <strong><?php echo $_SESSION['nomor_antrian']; ?></strong></p>
                <p>Booking ID: <strong><?php echo $_SESSION['booking_id']; ?></strong></p>
                <p>Silahkan datang ke bank sesuai dengan jadwal yang telah ditentukan dan tunjukkan nomor antrian Anda.</p>
                <a href="dashboard.php" class="btn btn-primary mt-3">Kembali ke Dashboard</a>
            </div>
            <?php else: ?>
            <!-- Tampilkan pesan error jika ada -->
            <?php if (isset($_SESSION['error_messages']) && !empty($_SESSION['error_messages'])): ?>
            <div class="alert alert-danger">
                <?php foreach ($_SESSION['error_messages'] as $err): ?>
                <div><?php echo htmlspecialchars($err); ?></div>
                <?php endforeach; ?>
            </div>
            <?php unset($_SESSION['error_messages']); endif; ?>

            <!-- Ambil data form jika terjadi error -->
            <?php $form_data = isset($_SESSION['form_data']) ? $_SESSION['form_data'] : []; unset($_SESSION['form_data']); ?>

            <form action="booking_process.php" method="post" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="nama">Nama Lengkap</label>
                            <input type="text" class="form-control" id="nama" name="nama" value="<?php echo isset($form_data['nama']) ? htmlspecialchars($form_data['nama']) : htmlspecialchars($user['nama']); ?>" readonly>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="no_identitas">Nomor Identitas</label>
                            <input type="text" class="form-control" id="no_identitas" name="no_identitas" value="<?php echo isset($form_data['no_identitas']) ? htmlspecialchars($form_data['no_identitas']) : htmlspecialchars($user['no_identitas']); ?>" readonly>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($form_data['email']) ? htmlspecialchars($form_data['email']) : htmlspecialchars($user['email']); ?>" readonly>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="no_hp">Nomor HP</label>
                            <input type="tel" class="form-control" id="no_hp" name="no_hp" value="<?php echo isset($form_data['no_hp']) ? htmlspecialchars($form_data['no_hp']) : htmlspecialchars($user['no_hp']); ?>" readonly>
                            <input type="hidden" name="nasabah_id" value="<?php echo $user['id']; ?>">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Pilih Layanan</label>

                    <input type="radio" name="layanan" id="teller" value="teller" class="service-option" required>
                    <label for="teller">
                        <span class="service-icon"><i class="fas fa-money-check-alt"></i></span>
                        <span class="service-title">Teller</span>
                        <div class="service-desc">Layanan transaksi keuangan seperti setor tunai, tarik tunai, transfer, dll.</div>
                    </label>

                    <input type="radio" name="layanan" id="cs" value="cs" class="service-option">
                    <label for="cs">
                        <span class="service-icon"><i class="fas fa-headset"></i></span>
                        <span class="service-title">Customer Service</span>
                        <div class="service-desc">Layanan pembukaan rekening, pengaduan nasabah, informasi produk, dll.</div>
                    </label>

                    <input type="radio" name="layanan" id="kredit" value="kredit" class="service-option">
                    <label for="kredit">
                        <span class="service-icon"><i class="fas fa-hand-holding-usd"></i></span>
                        <span class="service-title">Kredit</span>
                        <div class="service-desc">Layanan pengajuan kredit, konsultasi kredit, dll.</div>
                    </label>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="tanggal">Tanggal Kunjungan</label>
                            <input type="text" class="form-control" id="tanggal" name="tanggal" value="<?php echo isset($form_data['tanggal']) ? htmlspecialchars($form_data['tanggal']) : ''; ?>" required>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="waktu">Waktu Kunjungan</label>
                            <select class="form-control" id="waktu" name="waktu" required>
                                <option value="">Pilih Waktu</option>
                                <option value="08:00" <?php echo (isset($form_data['waktu']) && $form_data['waktu'] == '08:00') ? 'selected' : ''; ?>>08:00 - 09:00</option>
                                <option value="09:00" <?php echo (isset($form_data['waktu']) && $form_data['waktu'] == '09:00') ? 'selected' : ''; ?>>09:00 - 10:00</option>
                                <option value="10:00" <?php echo (isset($form_data['waktu']) && $form_data['waktu'] == '10:00') ? 'selected' : ''; ?>>10:00 - 11:00</option>
                                <option value="11:00" <?php echo (isset($form_data['waktu']) && $form_data['waktu'] == '11:00') ? 'selected' : ''; ?>>11:00 - 12:00</option>
                                <option value="13:00" <?php echo (isset($form_data['waktu']) && $form_data['waktu'] == '13:00') ? 'selected' : ''; ?>>13:00 - 14:00</option>
                                <option value="14:00" <?php echo (isset($form_data['waktu']) && $form_data['waktu'] == '14:00') ? 'selected' : ''; ?>>14:00 - 15:00</option>
                                <option value="15:00" <?php echo (isset($form_data['waktu']) && $form_data['waktu'] == '15:00') ? 'selected' : ''; ?>>15:00 - 16:00</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Upload KTP/Buku Tabungan</label>
                    <div class="upload-area" id="upload-area">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">
                            <h4>Klik atau seret file ke sini</h4>
                            <p>Format file: JPG, PNG, PDF (Maks. 2MB)</p>
                        </div>
                    </div>
                    <input type="file" id="file_upload" name="file_upload" style="display: none;" required>
                </div>

                <div class="form-group">
                    <label for="keterangan">Keterangan Tambahan (Opsional)</label>
                    <textarea class="form-control" id="keterangan" name="keterangan" rows="3"><?php echo isset($form_data['keterangan']) ? htmlspecialchars($form_data['keterangan']) : ''; ?></textarea>
                </div>

                <button type="submit" class="btn-submit">Booking Sekarang</button>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="footer-logo">
            <div class="footer-links">
                <a href="#">Tentang Kami</a>
                <a href="#">Syarat & Ketentuan</a>
                <a href="#">Kebijakan Privasi</a>
                <a href="#">Hubungi Kami</a>
            </div>
            <p class="copyright">© 2023 Bank BJB. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <script>
        $(document).ready(function() {
            // Initialize date picker
            flatpickr("#tanggal", {
                dateFormat: "Y-m-d",
                minDate: "today",
                maxDate: new Date().fp_incr(30), // Allow booking up to 30 days in advance
                disable: [
                    function(date) {
                        // No disabled days - allow booking on any day including weekends
                        return false;
                    }
                ]
            });

            // Handle file upload
            $("#upload-area").click(function() {
                $("#file_upload").click();
            });

            $("#file_upload").change(function() {
                if (this.files && this.files[0]) {
                    var fileName = this.files[0].name;
                    $(".upload-text h4").text("File dipilih: " + fileName);
                    $(".upload-icon i").removeClass("fa-cloud-upload-alt").addClass("fa-check-circle");
                    $(".upload-area").css("border-color", "#4caf50");
                }
            });
        });
    </script>
</body>
</html>
<?php endif; ?>
