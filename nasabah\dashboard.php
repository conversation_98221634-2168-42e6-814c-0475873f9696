<?php
// Nasabah dashboard page
$page_title = 'Dashboard Nasabah';
require_once '../includes/functions.php'; // pastikan functions.php yang asli digunakan
// require_once '../includes/functions_mock.php'; // HAPUS atau KOMENTARI baris ini agar tidak terjadi duplikasi fungsi

// Check if user is logged in as nasabah
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'nasabah') {
    // Set error message
    $_SESSION['error_message'] = 'Anda harus login terlebih dahulu untuk mengakses halaman ini.';
    // Redirect to login page
    redirect('login.php');
}

// Initialize error and success messages
$error_message = '';
$success_message = '';

// Check for messages in session
if (isset($_SESSION['error_message'])) {
    $error_message = $_SESSION['error_message'];
    unset($_SESSION['error_message']);
}

if (isset($_SESSION['success_message'])) {
    $success_message = $_SESSION['success_message'];
    unset($_SESSION['success_message']);
}

// Ambil data booking dari database
require_once '../includes/db.php';

// Ambil user_id dari session
$user_id = $_SESSION['user_id'];

// Ambil data user dari database
try {
    global $conn;

    // Ambil data nasabah berdasarkan user_id dari session
    $sql = "SELECT * FROM nasabah WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        // Override nama dengan data dari session jika ada
        if (isset($_SESSION['user_name'])) {
            $user['nama'] = $_SESSION['user_name'];
        }
        if (isset($_SESSION['user_email'])) {
            $user['email'] = $_SESSION['user_email'];
        }
    } else {
        // Jika data tidak ditemukan di database, buat data fallback
        $user = [
            'id' => $user_id,
            'nama' => $_SESSION['user_name'] ?? 'Nasabah',
            'email' => $_SESSION['user_email'] ?? '<EMAIL>',
            'no_identitas' => '****************',
            'no_hp' => '************',
            'status_verifikasi' => 'sudah'
        ];
        error_log("Failed to fetch user data for ID: $user_id");
    }

    // Ambil data booking berdasarkan nasabah_id yang sesuai dengan user yang login
    $bookings = get_bookings_by_nasabah_id($user_id);
    error_log("User ID: " . $user_id . ", Jumlah booking: " . count($bookings));

} catch (Exception $e) {
    error_log("Error fetching user data: " . $e->getMessage());
    $user = [
        'id' => $user_id,
        'nama' => $_SESSION['user_name'] ?? 'Nasabah',
        'email' => $_SESSION['user_email'] ?? '<EMAIL>',
        'no_identitas' => '****************',
        'no_hp' => '************',
        'status_verifikasi' => 'sudah'
    ];
    $bookings = [];
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Nasabah - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background-color: #f5f5f5;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Custom styles for flatpickr calendar */
        .flatpickr-weekdays {
            display: none !important;
        }

        .flatpickr-calendar .flatpickr-day {
            margin-top: 0;
        }

        .header {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .logo-container {
            display: flex;
            align-items: center;
        }

        .logo-container img {
            height: 40px;
            margin-right: 15px;
        }

        .logo-container h1 {
            font-size: 20px;
            margin: 0;
            font-weight: 600;
        }

        .user-menu {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .user-info {
            margin-right: 20px;
            text-align: right;
        }

        .user-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 0;
        }

        .user-role {
            font-size: 12px;
            opacity: 0.8;
        }

        .dropdown-toggle {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
        }

        .dropdown-toggle:focus {
            outline: none;
        }

        .dropdown-toggle i {
            font-size: 20px;
        }

        .dropdown-menu {
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: none;
            padding: 10px 0;
            margin-top: 10px;
        }

        .dropdown-item {
            padding: 8px 20px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .dropdown-item:hover {
            background-color: #f0f9f0;
            color: #4caf50;
        }

        .dropdown-item i {
            margin-right: 10px;
            color: #4caf50;
        }

        .sidebar {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-top: 30px;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .nav-pills .nav-link {
            color: #555;
            font-weight: 500;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 5px;
            transition: all 0.3s;
        }

        .nav-pills .nav-link:hover {
            background-color: #f0f9f0;
            color: #4caf50;
        }

        .nav-pills .nav-link.active {
            background-color: #4caf50;
            color: white;
        }

        .nav-pills .nav-link i {
            margin-right: 10px;
        }

        .content {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 30px;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        .content-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .dashboard-stats {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px 30px;
        }

        .stat-card {
            flex: 1;
            min-width: 200px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin: 0 10px 20px;
            display: flex;
            align-items: center;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 24px;
            color: white;
        }

        .stat-icon.blue {
            background: linear-gradient(135deg, #03a9f4 0%, #0288d1 100%);
        }

        .stat-icon.green {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
        }

        .stat-icon.orange {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }

        .stat-icon.red {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }

        .stat-info h4 {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .stat-info p {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 0;
        }

        .booking-list {
            margin-top: 30px;
        }

        .booking-card {
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s;
            border-left: 4px solid #4caf50;
        }

        .booking-card:hover {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transform: translateY(-3px);
        }

        .booking-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .booking-id {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .booking-status {
            margin-left: auto;
        }

        .status-badge {
            padding: 5px 15px;
            border-radius: 50px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-badge.confirmed {
            background-color: #e8f5e9;
            color: #388e3c;
        }

        .status-badge.pending {
            background-color: #fff8e1;
            color: #ffa000;
        }

        .status-badge.completed {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .status-badge.cancelled {
            background-color: #ffebee;
            color: #d32f2f;
        }

        .booking-details {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }

        .booking-detail {
            flex: 1;
            min-width: 200px;
            padding: 0 10px;
            margin-bottom: 10px;
        }

        .detail-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .detail-value {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .booking-actions {
            margin-top: 15px;
            display: flex;
            justify-content: flex-end;
        }

        .btn-action {
            padding: 5px 15px;
            border-radius: 50px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
            transition: all 0.3s;
        }

        .btn-action:hover {
            transform: translateY(-2px);
        }

        .btn-view {
            background-color: #e3f2fd;
            color: #1976d2;
            border: none;
        }

        .btn-view:hover {
            background-color: #bbdefb;
        }

        .btn-print {
            background-color: #e8f5e9;
            color: #388e3c;
            border: none;
        }

        .btn-print:hover {
            background-color: #c8e6c9;
        }

        .btn-cancel {
            background-color: #ffebee;
            color: #d32f2f;
            border: none;
        }

        .btn-cancel:hover {
            background-color: #ffcdd2;
        }

        .no-bookings {
            text-align: center;
            padding: 30px;
            color: #666;
        }

        .no-bookings i {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 15px;
        }

        .no-bookings p {
            font-size: 16px;
            margin-bottom: 20px;
        }

        .btn-new-booking {
            background-color: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-new-booking:hover {
            background-color: #388e3c;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        footer {
            background-color: #333;
            color: white;
            padding: 20px 0;
            text-align: center;
        }

        .copyright {
            opacity: 0.7;
            font-size: 14px;
            margin: 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="logo-container">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB">
                        <h1>Dashboard Nasabah Bank BJB</h1>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="user-menu">
                        <div class="user-info">
                            <p class="user-name"><?php echo $user['nama']; ?></p>
                            <p class="user-role">Nasabah</p>
                        </div>
                        <div class="dropdown">
                            <button class="dropdown-toggle" type="button" id="userDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-user-circle"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="profile.php"><i class="fas fa-user"></i> Profil</a>
                                <a class="dropdown-item" href="change_password.php"><i class="fas fa-lock"></i> Ubah Password</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="sidebar">
                    <h4 class="sidebar-title">Menu</h4>
                    <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                        <a class="nav-link active" id="dashboard-tab" data-toggle="pill" href="#dashboard" role="tab" aria-controls="dashboard" aria-selected="true">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <a class="nav-link" id="bookings-tab" data-toggle="pill" href="#bookings" role="tab" aria-controls="bookings" aria-selected="false">
                            <i class="fas fa-calendar-check"></i> Booking Saya
                        </a>
                        <a class="nav-link" id="new-booking-tab" data-toggle="pill" href="#new-booking" role="tab" aria-controls="new-booking" aria-selected="false">
                            <i class="fas fa-plus-circle"></i> Booking Baru
                        </a>
                        <a class="nav-link" id="profile-tab" data-toggle="pill" href="#profile" role="tab" aria-controls="profile" aria-selected="false">
                            <i class="fas fa-user"></i> Profil
                        </a>
                    </div>
                </div>
            </div>


            <!-- Content -->
            <div class="col-md-9">
                <div class="content">
                    <div class="tab-content" id="v-pills-tabContent">
                        <!-- Dashboard Tab -->
                        <div class="tab-pane fade show active" id="dashboard" role="tabpanel" aria-labelledby="dashboard-tab">
                            <h3 class="content-title">Dashboard</h3>

                            <?php if (!empty($error_message)): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-circle mr-2"></i> <?php echo $error_message; ?>
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($success_message)): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle mr-2"></i> <?php echo $success_message; ?>
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <?php endif; ?>

                            <div class="dashboard-stats">
                                <div class="stat-card">
                                    <div class="stat-icon blue">
                                        <i class="fas fa-calendar-check"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4>Total Booking</h4>
                                        <p><?php echo count($bookings); ?></p>
                                    </div>
                                </div>

                                <div class="stat-card">
                                    <div class="stat-icon green">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4>Booking Aktif</h4>
                                        <p>
                                            <?php
                                            $active_count = 0;
                                            foreach ($bookings as $booking) {
                                                if ($booking['status'] == 'confirmed' || $booking['status'] == 'pending') {
                                                    $active_count++;
                                                }
                                            }
                                            echo $active_count;
                                            ?>
                                        </p>
                                    </div>
                                </div>

                                <div class="stat-card">
                                    <div class="stat-icon orange">
                                        <i class="fas fa-history"></i>
                                    </div>
                                    <div class="stat-info">
                                        <h4>Booking Selesai</h4>
                                        <p>
                                            <?php
                                            $completed_count = 0;
                                            foreach ($bookings as $booking) {
                                                if ($booking['status'] == 'completed') {
                                                    $completed_count++;
                                                }
                                            }
                                            echo $completed_count;
                                            ?>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <h4>Booking Terbaru</h4>

                            <div class="booking-list">
                                <?php if (count($bookings) > 0): ?>
                                    <?php
                                    // Display only the 3 most recent bookings
                                    $recent_bookings = array_slice($bookings, 0, 3);
                                    foreach ($recent_bookings as $booking):
                                        // Format layanan name for display
                                        $layanan_display = '';
                                        switch ($booking['layanan']) {
                                            case 'teller':
                                                $layanan_display = 'Teller';
                                                break;
                                            case 'cs':
                                                $layanan_display = 'Customer Service';
                                                break;
                                            case 'kredit':
                                                $layanan_display = 'Kredit';
                                                break;
                                            default:
                                                $layanan_display = $booking['layanan'];
                                        }

                                        // Format date
                                        $tanggal_display = date('d F Y', strtotime($booking['tanggal']));

                                        // Format status badge
                                        $status_badge_class = '';
                                        $status_display = '';
                                        switch ($booking['status']) {
                                            case 'pending':
                                                $status_badge_class = 'pending';
                                                $status_display = 'Menunggu Konfirmasi';
                                                break;
                                            case 'confirmed':
                                                $status_badge_class = 'confirmed';
                                                $status_display = 'Terkonfirmasi';
                                                break;
                                            case 'completed':
                                                $status_badge_class = 'completed';
                                                $status_display = 'Selesai';
                                                break;
                                            case 'cancelled':
                                                $status_badge_class = 'cancelled';
                                                $status_display = 'Dibatalkan';
                                                break;
                                            default:
                                                $status_badge_class = 'pending';
                                                $status_display = $booking['status'];
                                        }
                                    ?>
                                    <div class="booking-card">
                                        <div class="booking-header">
                                            <div class="booking-id"><?php echo $booking['booking_id']; ?></div>
                                            <div class="booking-status">
                                                <span class="status-badge <?php echo $status_badge_class; ?>"><?php echo $status_display; ?></span>
                                            </div>
                                        </div>

                                        <div class="booking-details">
                                            <div class="booking-detail">
                                                <div class="detail-label">Layanan</div>
                                                <div class="detail-value"><?php echo $layanan_display; ?></div>
                                            </div>

                                            <div class="booking-detail">
                                                <div class="detail-label">Tanggal</div>
                                                <div class="detail-value"><?php echo $tanggal_display; ?></div>
                                            </div>

                                            <div class="booking-detail">
                                                <div class="detail-label">Waktu</div>
                                                <div class="detail-value"><?php echo $booking['waktu']; ?></div>
                                            </div>

                                            <div class="booking-detail">
                                                <div class="detail-label">Nomor Antrian</div>
                                                <div class="detail-value"><?php echo $booking['nomor_antrian']; ?></div>
                                            </div>
                                        </div>

                                        <div class="booking-actions">
                                            <a href="booking_detail.php?id=<?php echo $booking['booking_id']; ?>" class="btn btn-action btn-view">
                                                <i class="fas fa-eye"></i> Lihat Detail
                                            </a>
                                            <a href="print_ticket.php?id=<?php echo $booking['booking_id']; ?>" target="_blank" class="btn btn-action btn-print">
                                                <i class="fas fa-print"></i> Cetak Tiket
                                            </a>
                                            <?php if ($booking['status'] == 'pending' || $booking['status'] == 'confirmed'): ?>
                                            <a href="cancel_booking.php?id=<?php echo $booking['booking_id']; ?>" class="btn btn-action btn-cancel">
                                                <i class="fas fa-times"></i> Batalkan
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>

                                    <?php if (count($bookings) > 3): ?>
                                    <div class="text-center mt-4">
                                        <a href="#" class="btn btn-sm btn-outline-success" id="view-all-bookings">
                                            Lihat Semua Booking
                                        </a>
                                    </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <div class="no-bookings">
                                        <i class="fas fa-calendar-times"></i>
                                        <p>Anda belum memiliki booking antrian.</p>
                                        <a href="booking.php" class="btn btn-new-booking">
                                            <i class="fas fa-plus-circle mr-2"></i> Buat Booking Baru
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Bookings Tab -->
                        <div class="tab-pane fade" id="bookings" role="tabpanel" aria-labelledby="bookings-tab">
                            <h3 class="content-title">Booking Saya</h3>

                            <!-- Booking list will be loaded here -->
                            <div class="booking-list">
                                <!-- This will be populated with all bookings -->
                            </div>
                        </div>

                        <!-- New Booking Tab -->
                        <div class="tab-pane fade" id="new-booking" role="tabpanel" aria-labelledby="new-booking-tab">
                            <h3 class="content-title">Booking Baru</h3>

                            <!-- Booking form -->
                            <form action="booking_process.php" method="post" enctype="multipart/form-data" id="booking-form">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="nama">Nama Lengkap</label>
                                            <input type="text" class="form-control" id="nama" name="nama" value="<?php echo htmlspecialchars($user['nama']); ?>" readonly>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="no_identitas">Nomor Identitas</label>
                                            <input type="text" class="form-control" id="no_identitas" name="no_identitas" value="<?php echo htmlspecialchars($user['no_identitas']); ?>" readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="email">Email</label>
                                            <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" readonly>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="no_hp">Nomor HP</label>
                                            <input type="tel" class="form-control" id="no_hp" name="no_hp" value="<?php echo htmlspecialchars($user['no_hp']); ?>" readonly>
                                            <input type="hidden" name="nasabah_id" value="<?php echo $user['id']; ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>Pilih Layanan</label>

                                    <div class="custom-control custom-radio mb-3">
                                        <input type="radio" id="teller" name="layanan" value="teller" class="custom-control-input" required>
                                        <label class="custom-control-label" for="teller">
                                            <strong>Teller</strong> - Layanan transaksi keuangan seperti setor tunai, tarik tunai, transfer, dll.
                                        </label>
                                    </div>

                                    <div class="custom-control custom-radio mb-3">
                                        <input type="radio" id="cs" name="layanan" value="cs" class="custom-control-input">
                                        <label class="custom-control-label" for="cs">
                                            <strong>Customer Service</strong> - Layanan pembukaan rekening, pengaduan nasabah, informasi produk, dll.
                                        </label>
                                    </div>

                                    <div class="custom-control custom-radio mb-3">
                                        <input type="radio" id="kredit" name="layanan" value="kredit" class="custom-control-input">
                                        <label class="custom-control-label" for="kredit">
                                            <strong>Kredit</strong> - Layanan pengajuan kredit, konsultasi kredit, dll.
                                        </label>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="tanggal">Tanggal Kunjungan</label>
                                            <input type="text" class="form-control" id="tanggal" name="tanggal" required>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="waktu">Waktu Kunjungan</label>
                                            <select class="form-control" id="waktu" name="waktu" required>
                                                <option value="">Pilih Waktu</option>
                                                <option value="08:00">08:00 - 09:00</option>
                                                <option value="09:00">09:00 - 10:00</option>
                                                <option value="10:00">10:00 - 11:00</option>
                                                <option value="11:00">11:00 - 12:00</option>
                                                <option value="13:00">13:00 - 14:00</option>
                                                <option value="14:00">14:00 - 15:00</option>
                                                <option value="15:00">15:00 - 16:00</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="file_upload">Upload KTP/Buku Tabungan</label>
                                    <div class="custom-file">
                                        <input type="file" class="custom-file-input" id="file_upload" name="file_upload" required>
                                        <label class="custom-file-label" for="file_upload">Pilih file...</label>
                                    </div>
                                    <small class="form-text text-muted">Format file: JPG, PNG, PDF (Maks. 2MB)</small>
                                </div>

                                <div class="form-group">
                                    <label for="keterangan">Keterangan Tambahan (Opsional)</label>
                                    <textarea class="form-control" id="keterangan" name="keterangan" rows="3"></textarea>
                                </div>

                                <button type="submit" class="btn btn-success btn-block">
                                    <i class="fas fa-calendar-check mr-2"></i> Booking Sekarang
                                </button>
                            </form>
                        </div>

                        <!-- Profile Tab -->
                        <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                            <h3 class="content-title">Profil Saya</h3>

                            <!-- Profile form will be loaded here -->
                            <div class="text-center py-4">
                                <p>Halaman profil akan segera dimuat...</p>
                                <a href="profile.php" class="btn btn-success">
                                    <i class="fas fa-external-link-alt mr-2"></i> Buka Halaman Profil
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p class="copyright">© 2023 Bank BJB. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <script>
        $(document).ready(function() {
            // Handle "View All Bookings" button click
            $("#view-all-bookings").click(function(e) {
                e.preventDefault();
                $("#bookings-tab").tab("show");
            });



            // Load bookings when bookings tab is shown
            $('#bookings-tab').on('shown.bs.tab', function (e) {
                // In a real application, you would load bookings via AJAX
                // For demo purposes, we'll just copy the content
                var bookingList = $("#dashboard .booking-list").html();
                $("#bookings .booking-list").html(bookingList);
            });

            // Initialize date picker when new booking tab is shown
            $('#new-booking-tab').on('shown.bs.tab', function (e) {
                // Initialize date picker
                flatpickr("#tanggal", {
                    dateFormat: "Y-m-d",
                    minDate: "today",
                    maxDate: new Date().fp_incr(30), // Allow booking up to 30 days in advance
                    disable: [
                        function(date) {
                            // No disabled days - allow booking on any day including weekends
                            return false;
                        }
                    ],
                    // Hide the weekday names (SunMonTueWedThuFriSat)
                    locale: {
                        weekdays: {
                            shorthand: ['', '', '', '', '', '', ''],
                            longhand: ['', '', '', '', '', '', '']
                        }
                    }
                });
                // Handle file upload
                $(".custom-file-input").on("change", function() {
                    var fileName = $(this).val().split("\\").pop();
                    $(this).siblings(".custom-file-label").addClass("selected").html(fileName);
                });
            });

            // Handle form submission
            $("#booking-form").off("submit"); // Hapus handler JS lama agar tidak override submit asli
        });
    </script>
</body>
</html>

