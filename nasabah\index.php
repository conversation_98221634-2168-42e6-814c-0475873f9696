<?php
// Nasabah main page (Mock Version)
$page_title = 'Booking Online Nasabah';
require_once '../includes/functions.php';
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Online - Bank BJB</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        body {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .header {
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 100;
        }

        .logo-container {
            display: flex;
            align-items: center;
        }

        .logo-container img {
            height: 50px;
            margin-right: 15px;
        }

        .logo-container h1 {
            color: white;
            font-size: 24px;
            margin: 0;
            font-weight: 600;
        }

        .nav-menu {
            display: flex;
            justify-content: flex-end;
        }

        .nav-menu a {
            color: white;
            margin-left: 20px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            padding: 8px 15px;
            border-radius: 50px;
        }

        .nav-menu a:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }

        .nav-menu a.active {
            background-color: white;
            color: #4caf50;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .nav-menu a i {
            margin-right: 5px;
        }

        .hero-section {
            padding: 80px 0;
            text-align: center;
            color: white;
        }

        .hero-section h2 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .hero-section p {
            font-size: 18px;
            max-width: 700px;
            margin: 0 auto 30px;
            opacity: 0.9;
        }

        .btn-hero {
            background-color: white;
            color: #4caf50;
            border: none;
            padding: 12px 30px;
            font-size: 18px;
            font-weight: 600;
            border-radius: 50px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .btn-hero:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: #388e3c;
        }

        .btn-outline {
            background-color: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-outline:hover {
            background-color: white;
            color: #4caf50;
        }

        .features-section {
            background-color: white;
            padding: 80px 0;
            border-radius: 30px 30px 0 0;
            margin-top: -30px;
            position: relative;
            z-index: 10;
            box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            text-align: center;
            margin-bottom: 50px;
        }

        .section-title h3 {
            font-size: 36px;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }

        .section-title p {
            color: #666;
            max-width: 700px;
            margin: 0 auto;
        }

        .feature-card {
            background-color: #f9f9f9;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s;
            height: 100%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 30px;
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .feature-card h4 {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .feature-card p {
            color: #666;
            margin-bottom: 0;
        }

        .steps-section {
            padding: 80px 0;
            background-color: #f5f5f5;
        }

        .step-card {
            display: flex;
            align-items: flex-start;
            margin-bottom: 30px;
        }

        .step-number {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 700;
            margin-right: 20px;
            flex-shrink: 0;
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .step-content h4 {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .step-content p {
            color: #666;
            margin-bottom: 0;
        }

        .cta-section {
            padding: 80px 0;
            text-align: center;
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            color: white;
        }

        .cta-section h3 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .cta-section p {
            font-size: 18px;
            max-width: 700px;
            margin: 0 auto 30px;
            opacity: 0.9;
        }

        footer {
            background-color: #333;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        .footer-logo {
            height: 60px;
            margin-bottom: 20px;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .footer-links a {
            color: white;
            margin: 0 15px;
            text-decoration: none;
            transition: all 0.3s;
        }

        .footer-links a:hover {
            color: #4caf50;
        }

        .copyright {
            opacity: 0.7;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="logo-container">
                        <img src="../assets/img/logo.jpeg" alt="Bank BJB">
                        <h1>Bank BJB</h1>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="nav-menu">
                        <a href="#" class="active"><i class="fas fa-home"></i> Beranda</a>
                        <a href="booking.php"><i class="fas fa-calendar-check"></i> Booking</a>
                        <a href="check_status.php"><i class="fas fa-search"></i> Cek Status</a>
                        <a href="register.php"><i class="fas fa-user-plus"></i> Registrasi</a>
                        <a href="login.php"><i class="fas fa-sign-in-alt"></i> Login</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h2>Booking Antrian Bank BJB</h2>
            <p>Layanan booking antrian online untuk kenyamanan Anda. Tidak perlu menunggu lama, cukup booking dari rumah dan datang sesuai jadwal.</p>
            <a href="booking.php" class="btn-hero">Booking Sekarang</a>
            <a href="register.php" class="btn-hero btn-outline">Registrasi Akun</a>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="section-title">
                <h3>Keunggulan Layanan Kami</h3>
                <p>Nikmati berbagai kemudahan dalam menggunakan layanan booking antrian online Bank BJB</p>
            </div>

            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h4>Hemat Waktu</h4>
                        <p>Tidak perlu menunggu lama di bank, cukup datang sesuai jadwal booking Anda</p>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h4>Fleksibel</h4>
                        <p>Pilih tanggal dan waktu yang sesuai dengan jadwal Anda</p>
                    </div>
                </div>

                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4>Aman & Terpercaya</h4>
                        <p>Verifikasi identitas dengan KTP/buku tabungan untuk keamanan transaksi</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Steps Section -->
    <section class="steps-section">
        <div class="container">
            <div class="section-title">
                <h3>Cara Booking Antrian</h3>
                <p>Ikuti langkah-langkah mudah berikut untuk booking antrian online</p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="step-card">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>Registrasi Akun</h4>
                            <p>Daftar akun dengan data diri yang valid untuk verifikasi identitas</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="step-card">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>Pilih Layanan</h4>
                            <p>Pilih jenis layanan yang Anda butuhkan (Teller, Customer Service, atau Kredit)</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="step-card">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>Pilih Tanggal & Waktu</h4>
                            <p>Tentukan tanggal dan estimasi waktu kedatangan Anda</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="step-card">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4>Verifikasi KTP/Buku Tabungan</h4>
                            <p>Unggah foto KTP atau buku tabungan untuk verifikasi identitas</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="step-card">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <h4>Dapatkan Nomor Antrian</h4>
                            <p>Nomor antrian akan dikirim melalui email dan dapat dilihat di akun Anda</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <h3>Mulai Booking Antrian Sekarang</h3>
            <p>Nikmati kemudahan dan kenyamanan dalam bertransaksi di Bank BJB</p>
            <a href="booking.php" class="btn-hero">Booking Sekarang</a>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <img src="../assets/img/logo.jpeg" alt="Bank BJB" class="footer-logo">
            <div class="footer-links">
                <a href="#">Tentang Kami</a>
                <a href="#">Syarat & Ketentuan</a>
                <a href="#">Kebijakan Privasi</a>
                <a href="#">Hubungi Kami</a>
            </div>
            <p class="copyright">© 2023 Bank BJB. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
