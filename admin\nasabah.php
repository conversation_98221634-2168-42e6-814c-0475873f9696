<?php
// Customer management page (Mock Version)
$page_title = 'Manajemen Nasabah';
$active_menu = 'nasabah';
$is_admin = true;
require_once '../includes/functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('../login_admin.php');
}

// Check if user is admin
if (!is_admin()) {
    redirect('../index.php');
}

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['verify'])) {
        try {
            $customer_id = (int)$_POST['customer_id'];
            $status = sanitize($_POST['status']);
            $reason = isset($_POST['reason']) ? sanitize($_POST['reason']) : '';

            // Update customer verification status
            $sql = "UPDATE nasabah SET status_verifikasi = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("si", $status, $customer_id);

            if ($stmt->execute()) {
                $_SESSION['success_message'] = 'Status verifikasi nasabah berhasil diperbarui.';
            } else {
                $_SESSION['error_message'] = 'Gagal memperbarui status verifikasi nasabah.';
            }

            redirect('nasabah.php' . (isset($_GET['filter']) ? '?filter=' . $_GET['filter'] : ''));
        } catch (Exception $e) {
            $_SESSION['error_message'] = 'Terjadi kesalahan: ' . $e->getMessage();
            redirect('nasabah.php');
        }
    } elseif (isset($_POST['block'])) {
        try {
            $customer_id = (int)$_POST['customer_id'];
            $block_status = (int)$_POST['block_status'];

            // Update customer block status
            $sql = "UPDATE nasabah SET is_blocked = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ii", $block_status, $customer_id);

            if ($stmt->execute()) {
                if ($block_status == 1) {
                    $_SESSION['success_message'] = 'Nasabah berhasil diblokir.';
                } else {
                    $_SESSION['success_message'] = 'Blokir nasabah berhasil dibatalkan.';
                }
            } else {
                $_SESSION['error_message'] = 'Gagal memperbarui status blokir nasabah.';
            }

            redirect('nasabah.php' . (isset($_GET['filter']) ? '?filter=' . $_GET['filter'] : ''));
        } catch (Exception $e) {
            $_SESSION['error_message'] = 'Terjadi kesalahan: ' . $e->getMessage();
            redirect('nasabah.php');
        }
    }
}

// Get filter and search parameters
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$sort = isset($_GET['sort']) ? sanitize($_GET['sort']) : 'id_desc';

// Get data from database
$customers = [];
$where_clauses = [];
$params = [];

// Build WHERE clause based on filter
if ($filter == 'belum') {
    $where_clauses[] = "status_verifikasi = 'belum'";
} elseif ($filter == 'terverifikasi' || $filter == 'sudah') {
    $where_clauses[] = "status_verifikasi = 'sudah'";
} elseif ($filter == 'ditolak') {
    $where_clauses[] = "status_verifikasi = 'ditolak'";
} elseif ($filter == 'blocked') {
    $where_clauses[] = "is_blocked = 1";
} elseif ($filter == 'active') {
    $where_clauses[] = "last_visit >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
} elseif ($filter == 'inactive') {
    $where_clauses[] = "(last_visit < DATE_SUB(CURDATE(), INTERVAL 30 DAY) OR last_visit IS NULL)";
}

// Add search condition if provided
if (!empty($search)) {
    $where_clauses[] = "(nama LIKE ? OR no_identitas LIKE ? OR email LIKE ? OR no_hp LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

// Build the complete WHERE clause
$where_sql = '';
if (!empty($where_clauses)) {
    $where_sql = "WHERE " . implode(" AND ", $where_clauses);
}

// Determine sort order
$order_by = "id DESC"; // Default sort
if ($sort == 'id_asc') {
    $order_by = "id ASC";
} elseif ($sort == 'nama_asc') {
    $order_by = "nama ASC";
} elseif ($sort == 'nama_desc') {
    $order_by = "nama DESC";
} elseif ($sort == 'tanggal_desc') {
    $order_by = "created_at DESC";
} elseif ($sort == 'tanggal_asc') {
    $order_by = "created_at ASC";
}

// Query to get customers
$sql = "SELECT * FROM nasabah $where_sql ORDER BY $order_by";

try {
    if (!empty($params)) {
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->get_result();
    } else {
        $result = query($sql);
    }

    if ($result && num_rows($result) > 0) {
        while ($row = fetch_assoc($result)) {
            // Get statistics for each customer
            $customer_id = $row['id'];

            // Get total antrian
            $sql_antrian = "SELECT COUNT(*) as total FROM antrian WHERE nasabah_id = $customer_id";
            $result_antrian = query($sql_antrian);
            $total_antrian = 0;
            if ($result_antrian && num_rows($result_antrian) > 0) {
                $row_antrian = fetch_assoc($result_antrian);
                $total_antrian = $row_antrian['total'];
            }

            // Get total booking
            $sql_booking = "SELECT COUNT(*) as total FROM antrian WHERE nasabah_id = $customer_id AND booking_dari = 'online'";
            $result_booking = query($sql_booking);
            $total_booking = 0;
            if ($result_booking && num_rows($result_booking) > 0) {
                $row_booking = fetch_assoc($result_booking);
                $total_booking = $row_booking['total'];
            }

            // Get total selesai
            $sql_selesai = "SELECT COUNT(*) as total FROM antrian WHERE nasabah_id = $customer_id AND status = 'selesai'";
            $result_selesai = query($sql_selesai);
            $total_selesai = 0;
            if ($result_selesai && num_rows($result_selesai) > 0) {
                $row_selesai = fetch_assoc($result_selesai);
                $total_selesai = $row_selesai['total'];
            }

            // Get last visit
            $sql_last_visit = "SELECT MAX(tanggal) as last_visit FROM antrian WHERE nasabah_id = $customer_id";
            $result_last_visit = query($sql_last_visit);
            $last_visit = null;
            if ($result_last_visit && num_rows($result_last_visit) > 0) {
                $row_last_visit = fetch_assoc($result_last_visit);
                $last_visit = $row_last_visit['last_visit'];
            }

            // Add statistics to customer data
            $row['total_antrian'] = $total_antrian;
            $row['total_booking'] = $total_booking;
            $row['total_selesai'] = $total_selesai;
            $row['last_visit'] = $last_visit;

            $customers[] = $row;
        }
    }
} catch (Exception $e) {
    $error_message = "Terjadi kesalahan saat mengambil data nasabah: " . $e->getMessage();
}

// Fallback to mock data if no customers found or error occurred
if (empty($customers) && empty($error_message)) {
    // This is just a fallback, we already tried to get real data above
    $error_message = "Tidak ada data nasabah yang ditemukan.";
}

// Include header
include_once 'includes/header.php';
?>

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>Manajemen Nasabah</h1>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-filter"></i> Filter & Pencarian
            </div>
            <div class="card-body">
                <form method="get" action="nasabah.php" class="mb-3">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Cari nama, no. identitas, email, atau no. HP" name="search" value="<?php echo $search; ?>">
                                <div class="input-group-append">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i> Cari
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" name="sort" onchange="this.form.submit()">
                                <option value="id_desc" <?php echo $sort == 'id_desc' ? 'selected' : ''; ?>>ID (Terbaru)</option>
                                <option value="id_asc" <?php echo $sort == 'id_asc' ? 'selected' : ''; ?>>ID (Terlama)</option>
                                <option value="nama_asc" <?php echo $sort == 'nama_asc' ? 'selected' : ''; ?>>Nama (A-Z)</option>
                                <option value="nama_desc" <?php echo $sort == 'nama_desc' ? 'selected' : ''; ?>>Nama (Z-A)</option>
                                <option value="tanggal_desc" <?php echo $sort == 'tanggal_desc' ? 'selected' : ''; ?>>Tanggal Daftar (Terbaru)</option>
                                <option value="tanggal_asc" <?php echo $sort == 'tanggal_asc' ? 'selected' : ''; ?>>Tanggal Daftar (Terlama)</option>
                                <option value="antrian_desc" <?php echo $sort == 'antrian_desc' ? 'selected' : ''; ?>>Total Antrian (Terbanyak)</option>
                                <option value="antrian_asc" <?php echo $sort == 'antrian_asc' ? 'selected' : ''; ?>>Total Antrian (Tersedikit)</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <a href="nasabah.php" class="btn btn-secondary btn-block">
                                <i class="fas fa-sync"></i> Reset Filter
                            </a>
                        </div>
                    </div>

                    <!-- Hidden field to preserve filter when sorting -->
                    <?php if(!empty($filter)): ?>
                    <input type="hidden" name="filter" value="<?php echo $filter; ?>">
                    <?php endif; ?>
                </form>

                <div class="btn-group btn-group-sm d-flex flex-wrap">
                    <a href="nasabah.php<?php echo !empty($search) ? '?search=' . $search : ''; ?>" class="btn btn-outline-primary <?php echo $filter == '' ? 'active' : ''; ?>">
                        <i class="fas fa-users"></i> Semua Nasabah
                    </a>
                    <a href="nasabah.php?filter=belum<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-warning <?php echo $filter == 'belum' ? 'active' : ''; ?>">
                        <i class="fas fa-clock"></i> Menunggu Verifikasi
                    </a>
                    <a href="nasabah.php?filter=terverifikasi<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-success <?php echo $filter == 'terverifikasi' ? 'active' : ''; ?>">
                        <i class="fas fa-check"></i> Terverifikasi
                    </a>
                    <a href="nasabah.php?filter=ditolak<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-danger <?php echo $filter == 'ditolak' ? 'active' : ''; ?>">
                        <i class="fas fa-times"></i> Ditolak
                    </a>
                    <a href="nasabah.php?filter=blocked<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-dark <?php echo $filter == 'blocked' ? 'active' : ''; ?>">
                        <i class="fas fa-ban"></i> Diblokir
                    </a>
                    <a href="nasabah.php?filter=active<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-info <?php echo $filter == 'active' ? 'active' : ''; ?>">
                        <i class="fas fa-user-check"></i> Aktif (30 Hari)
                    </a>
                    <a href="nasabah.php?filter=inactive<?php echo !empty($search) ? '&search=' . $search : ''; ?>" class="btn btn-outline-secondary <?php echo $filter == 'inactive' ? 'active' : ''; ?>">
                        <i class="fas fa-user-clock"></i> Tidak Aktif
                    </a>
                </div>

                <?php if(!empty($search)): ?>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-search"></i> Hasil pencarian untuk: <strong><?php echo $search; ?></strong>
                    <a href="nasabah.php<?php echo !empty($filter) ? '?filter=' . $filter : ''; ?>" class="float-right">
                        <i class="fas fa-times"></i> Hapus Pencarian
                    </a>
                </div>
                <?php endif; ?>

                <div class="mt-3">
                    <small class="text-muted">
                        Menampilkan <?php echo count($customers); ?> nasabah
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <i class="fas fa-users"></i> Daftar Nasabah
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nama</th>
                        <th>No. Identitas</th>
                        <th>Kontak</th>
                        <th>Status</th>
                        <th>Statistik</th>
                        <th>Terakhir Kunjungan</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(count($customers) > 0): ?>
                    <?php foreach($customers as $customer): ?>
                    <tr>
                        <td><?php echo $customer['id']; ?></td>
                        <td>
                            <strong><?php echo $customer['nama']; ?></strong>
                            <?php if(isset($customer['tanggal_lahir']) && $customer['tanggal_lahir']): ?>
                            <br><small class="text-muted"><?php echo date('d/m/Y', strtotime($customer['tanggal_lahir'])); ?></small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge badge-info">
                                <?php
                                if ($customer['jenis_identitas'] == 'ktp') {
                                    echo 'KTP';
                                } else {
                                    echo 'Buku Tabungan';
                                }
                                ?>
                            </span>
                            <?php echo $customer['no_identitas']; ?>
                        </td>
                        <td>
                            <?php if($customer['no_hp']): ?>
                            <i class="fas fa-phone"></i> <?php echo $customer['no_hp']; ?><br>
                            <?php endif; ?>

                            <?php if($customer['email']): ?>
                            <i class="fas fa-envelope"></i> <?php echo $customer['email']; ?>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($customer['status_verifikasi'] == 'belum'): ?>
                            <span class="badge badge-warning">Menunggu Verifikasi</span>
                            <?php elseif($customer['status_verifikasi'] == 'sudah'): ?>
                            <span class="badge badge-success">Terverifikasi</span>
                            <?php else: ?>
                            <span class="badge badge-danger">Ditolak</span>
                            <?php endif; ?>

                            <?php if($customer['is_blocked']): ?>
                            <br><span class="badge badge-dark">Diblokir</span>
                            <?php endif; ?>

                            <br><small class="text-muted">Terdaftar: <?php echo date('d/m/Y', strtotime($customer['created_at'])); ?></small>
                        </td>
                        <td>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <i class="fas fa-ticket-alt text-primary" title="Total Antrian"></i>
                                </div>
                                <div class="text-right">
                                    <strong><?php echo $customer['total_antrian']; ?></strong>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <i class="fas fa-calendar-check text-success" title="Booking Online"></i>
                                </div>
                                <div class="text-right">
                                    <strong><?php echo $customer['total_booking']; ?></strong>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <i class="fas fa-check-circle text-info" title="Antrian Selesai"></i>
                                </div>
                                <div class="text-right">
                                    <strong><?php echo $customer['total_selesai']; ?></strong>
                                </div>
                            </div>
                        </td>
                        <td>
                            <?php if($customer['last_visit']): ?>
                            <?php echo date('d/m/Y', strtotime($customer['last_visit'])); ?>
                            <?php else: ?>
                            <span class="text-muted">Belum pernah</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    Aksi
                                </button>
                                <div class="dropdown-menu">
                                    <?php if($customer['file_identitas']): ?>
                                    <a class="dropdown-item btn-preview-id" href="#" data-toggle="modal" data-target="#previewModal" data-id="<?php echo $customer['id']; ?>" data-image="../uploads/identitas/<?php echo $customer['file_identitas']; ?>">
                                        <i class="fas fa-eye"></i> Lihat Identitas
                                    </a>
                                    <?php endif; ?>

                                    <?php if($customer['status_verifikasi'] == 'belum'): ?>
                                    <a class="dropdown-item" href="#" data-toggle="modal" data-target="#verifyModal" data-id="<?php echo $customer['id']; ?>" data-name="<?php echo $customer['nama']; ?>">
                                        <i class="fas fa-check"></i> Verifikasi
                                    </a>
                                    <?php endif; ?>

                                    <a class="dropdown-item" href="antrian_nasabah.php?id=<?php echo $customer['id']; ?>">
                                        <i class="fas fa-list"></i> Lihat Antrian
                                    </a>

                                    <?php if($customer['is_blocked']): ?>
                                    <a class="dropdown-item text-success" href="#" data-toggle="modal" data-target="#blockModal" data-id="<?php echo $customer['id']; ?>" data-name="<?php echo $customer['nama']; ?>" data-status="0">
                                        <i class="fas fa-unlock"></i> Batal Blokir
                                    </a>
                                    <?php else: ?>
                                    <a class="dropdown-item text-danger" href="#" data-toggle="modal" data-target="#blockModal" data-id="<?php echo $customer['id']; ?>" data-name="<?php echo $customer['nama']; ?>" data-status="1">
                                        <i class="fas fa-ban"></i> Blokir
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php else: ?>
                    <tr>
                        <td colspan="8" class="text-center">Tidak ada data nasabah.</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">Preview Identitas</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img src="" alt="Preview Identitas" class="img-fluid" id="previewImage">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Verify Modal -->
<div class="modal fade" id="verifyModal" tabindex="-1" role="dialog" aria-labelledby="verifyModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="verifyModalLabel">Verifikasi Nasabah</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="nasabah.php?filter=<?php echo $filter; ?>" method="post">
                <div class="modal-body">
                    <p>Anda akan memverifikasi nasabah: <strong id="verifyName"></strong></p>

                    <div class="form-group">
                        <label for="status">Status Verifikasi</label>
                        <select class="form-control" id="status" name="status" required>
                            <option value="sudah">Terverifikasi</option>
                            <option value="ditolak">Ditolak</option>
                        </select>
                    </div>

                    <div class="form-group" id="reasonGroup" style="display: none;">
                        <label for="reason">Alasan Penolakan</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3"></textarea>
                    </div>

                    <input type="hidden" id="verifyCustomerId" name="customer_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" name="verify" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Block Modal -->
<div class="modal fade" id="blockModal" tabindex="-1" role="dialog" aria-labelledby="blockModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="blockModalLabel">Blokir Nasabah</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="nasabah.php?filter=<?php echo $filter; ?>" method="post">
                <div class="modal-body">
                    <p id="blockText">Anda akan memblokir nasabah: <strong id="blockName"></strong></p>
                    <p class="text-danger" id="blockWarning">Nasabah yang diblokir tidak akan dapat membuat antrian baru.</p>

                    <input type="hidden" id="blockCustomerId" name="customer_id">
                    <input type="hidden" id="blockStatus" name="block_status" value="1">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" name="block" class="btn btn-danger" id="blockButton">Blokir</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'includes/footer.php';
?>

<script>
$(document).ready(function() {
    // Preview modal
    $('#previewModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var image = button.data('image');
        var modal = $(this);

        modal.find('#previewImage').attr('src', image);
    });

    // Verify modal
    $('#verifyModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var id = button.data('id');
        var name = button.data('name');
        var modal = $(this);

        modal.find('#verifyName').text(name);
        modal.find('#verifyCustomerId').val(id);
    });

    // Show/hide reason field based on status
    $('#status').on('change', function() {
        if ($(this).val() === 'ditolak') {
            $('#reasonGroup').show();
            $('#reason').attr('required', true);
        } else {
            $('#reasonGroup').hide();
            $('#reason').attr('required', false);
        }
    });

    // Block modal
    $('#blockModal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget);
        var id = button.data('id');
        var name = button.data('name');
        var status = button.data('status');
        var modal = $(this);

        modal.find('#blockName').text(name);
        modal.find('#blockCustomerId').val(id);
        modal.find('#blockStatus').val(status);

        if (status === 0) {
            modal.find('#blockModalLabel').text('Batal Blokir Nasabah');
            modal.find('#blockText').text('Anda akan membatalkan blokir nasabah: ');
            modal.find('#blockWarning').hide();
            modal.find('#blockButton').removeClass('btn-danger').addClass('btn-success').text('Batal Blokir');
        } else {
            modal.find('#blockModalLabel').text('Blokir Nasabah');
            modal.find('#blockText').text('Anda akan memblokir nasabah: ');
            modal.find('#blockWarning').show();
            modal.find('#blockButton').removeClass('btn-success').addClass('btn-danger').text('Blokir');
        }
    });
});
</script>
