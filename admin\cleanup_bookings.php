<?php
/**
 * Cleanup Bookings
 * 
 * Script untuk membersihkan data booking yang tidak sesuai dengan nasabah yang login
 */

require_once '../includes/functions.php';
require_once '../includes/db.php';

// Check if user is logged in as admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    $_SESSION['error_message'] = 'Anda harus login sebagai admin untuk mengakses halaman ini.';
    redirect('login.php');
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['cleanup_orphaned'])) {
        // Hapus booking yang tidak memiliki nasabah_id yang valid
        $sql = "DELETE FROM bookings WHERE nasabah_id NOT IN (SELECT id FROM nasabah)";
        $result = $conn->query($sql);
        
        if ($result) {
            $affected_rows = $conn->affected_rows;
            $message = "Berhasil menghapus $affected_rows booking yang tidak memiliki nasabah valid.";
        } else {
            $error = "Gagal menghapus booking: " . $conn->error;
        }
    }
    
    if (isset($_POST['cleanup_test_data'])) {
        // Hapus data test/mock yang mungkin ada
        $sql = "DELETE FROM bookings WHERE booking_id LIKE 'BK%' AND created_at < DATE_SUB(NOW(), INTERVAL 1 DAY)";
        $result = $conn->query($sql);
        
        if ($result) {
            $affected_rows = $conn->affected_rows;
            $message = "Berhasil menghapus $affected_rows booking test data.";
        } else {
            $error = "Gagal menghapus booking test data: " . $conn->error;
        }
    }
    
    if (isset($_POST['reset_all_bookings'])) {
        // Hapus semua booking (hati-hati!)
        $sql = "DELETE FROM bookings";
        $result = $conn->query($sql);
        
        if ($result) {
            $affected_rows = $conn->affected_rows;
            $message = "Berhasil menghapus semua booking ($affected_rows records).";
        } else {
            $error = "Gagal menghapus semua booking: " . $conn->error;
        }
    }
}

// Get statistics
$stats = [];

// Total bookings
$sql = "SELECT COUNT(*) as total FROM bookings";
$result = $conn->query($sql);
$stats['total_bookings'] = $result->fetch_assoc()['total'];

// Orphaned bookings (booking tanpa nasabah valid)
$sql = "SELECT COUNT(*) as total FROM bookings WHERE nasabah_id NOT IN (SELECT id FROM nasabah)";
$result = $conn->query($sql);
$stats['orphaned_bookings'] = $result->fetch_assoc()['total'];

// Test data bookings
$sql = "SELECT COUNT(*) as total FROM bookings WHERE booking_id LIKE 'BK%' AND created_at < DATE_SUB(NOW(), INTERVAL 1 DAY)";
$result = $conn->query($sql);
$stats['test_bookings'] = $result->fetch_assoc()['total'];

// Include header
include 'includes/header.php';
?>

<h2 class="content-title mb-4">Cleanup Bookings</h2>

<?php if (!empty($message)): ?>
<div class="alert alert-success alert-dismissible fade show" role="alert">
    <i class="fas fa-check-circle mr-2"></i> <?php echo $message; ?>
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<?php endif; ?>

<?php if (!empty($error)): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="fas fa-exclamation-circle mr-2"></i> <?php echo $error; ?>
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Statistik Booking</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="stat-card bg-primary text-white">
                            <div class="stat-number"><?php echo $stats['total_bookings']; ?></div>
                            <div class="stat-label">Total Booking</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card bg-warning text-white">
                            <div class="stat-number"><?php echo $stats['orphaned_bookings']; ?></div>
                            <div class="stat-label">Booking Tanpa Nasabah</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card bg-info text-white">
                            <div class="stat-number"><?php echo $stats['test_bookings']; ?></div>
                            <div class="stat-label">Test Data Lama</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Aksi Cleanup</h5>
            </div>
            <div class="card-body">
                <form method="post" onsubmit="return confirm('Apakah Anda yakin ingin melakukan aksi ini?');">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="cleanup-action">
                                <h6>Hapus Booking Tanpa Nasabah</h6>
                                <p class="text-muted">Menghapus booking yang tidak memiliki nasabah_id yang valid di database.</p>
                                <button type="submit" name="cleanup_orphaned" class="btn btn-warning btn-sm">
                                    <i class="fas fa-trash mr-1"></i> Hapus Orphaned Bookings
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="cleanup-action">
                                <h6>Hapus Test Data Lama</h6>
                                <p class="text-muted">Menghapus booking test yang dibuat lebih dari 1 hari yang lalu.</p>
                                <button type="submit" name="cleanup_test_data" class="btn btn-info btn-sm">
                                    <i class="fas fa-trash mr-1"></i> Hapus Test Data
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="cleanup-action">
                                <h6>Reset Semua Booking</h6>
                                <p class="text-muted text-danger">HATI-HATI! Menghapus semua data booking.</p>
                                <button type="submit" name="reset_all_bookings" class="btn btn-danger btn-sm">
                                    <i class="fas fa-exclamation-triangle mr-1"></i> Reset Semua
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.stat-card {
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 20px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.cleanup-action {
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-bottom: 15px;
    height: 100%;
}

.cleanup-action h6 {
    color: #333;
    margin-bottom: 10px;
}

.cleanup-action p {
    font-size: 0.9rem;
    margin-bottom: 15px;
}
</style>

<?php
// Include footer
include 'includes/footer.php';
?>
