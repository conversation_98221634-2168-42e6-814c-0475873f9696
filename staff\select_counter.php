<?php
// Process counter selection (Mock Version)
require_once '../includes/functions.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('../login.php');
}

// Check if user is staff
if (!is_staff()) {
    redirect('../index.php');
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get counter ID
    $counter_id = isset($_POST['counter_id']) ? (int)$_POST['counter_id'] : 0;

    // Validate counter ID
    if ($counter_id < 1 || $counter_id > 7) {
        $_SESSION['error_message'] = 'ID loket tidak valid.';
        redirect('index_mock.php');
    }

    // Get counter data
    $counters = [
        1 => ['nama_loket' => 'Loket 1', 'jenis_layanan' => 'teller'],
        2 => ['nama_loket' => 'Loket 2', 'jenis_layanan' => 'teller'],
        3 => ['nama_loket' => 'Loket 3', 'jenis_layanan' => 'teller'],
        4 => ['nama_loket' => 'Loket 4', 'jenis_layanan' => 'cs'],
        5 => ['nama_loket' => 'Loket 5', 'jenis_layanan' => 'cs'],
        6 => ['nama_loket' => 'Loket 6', 'jenis_layanan' => 'kredit'],
        7 => ['nama_loket' => 'Loket 7', 'jenis_layanan' => 'kredit']
    ];

    // Set session variables
    $_SESSION['counter_id'] = $counter_id;
    $_SESSION['counter_name'] = $counters[$counter_id]['nama_loket'];
    $_SESSION['counter_type'] = $counters[$counter_id]['jenis_layanan'];

    // Set success message
    $_SESSION['success_message'] = 'Loket berhasil dipilih: ' . $counters[$counter_id]['nama_loket'];

    // Redirect to dashboard
    redirect('index_mock.php');
} else {
    // If not submitted via POST, redirect to dashboard
    redirect('index_mock.php');
}
?>
